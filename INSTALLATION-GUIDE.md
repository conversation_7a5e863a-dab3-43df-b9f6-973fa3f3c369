# 🚀 Hướng dẫn cài đặt Green Image Viewer Extension

## ✅ Kiểm tra files cần thiết

Đ<PERSON><PERSON> bảo thư mục có đầy đủ files sau:
- ✅ `manifest.json` - File cấu hình chính
- ✅ `popup.html` - Gia<PERSON> diện popup
- ✅ `icons/icon16.png` - Icon 16x16px
- ✅ `icons/icon32.png` - Icon 32x32px  
- ✅ `icons/icon48.png` - Icon 48x48px
- ✅ `icons/icon128.png` - Icon 128x128px

## 📋 Các bước cài đặt

### Bước 1: Mở Chrome Extensions
1. Mở Chrome browser
2. Vào địa chỉ: `chrome://extensions/`
3. Hoặc: Menu ⋮ → More tools → Extensions

### Bước 2: Bật Developer Mode
1. Tìm toggle "Developer mode" ở góc trên bên phải
2. Bật ON (màu xanh)

### Bước 3: Load Extension
1. Click nút "Load unpacked"
2. <PERSON><PERSON><PERSON> thư mục `E:\chrome\green`
3. Click "Select Folder"

### Bước 4: Kiểm tra
- Extension xuất hiện trong danh sách
- Icon hiển thị trên toolbar
- Không có error messages

## 🧪 Test Extension

### Test cơ bản:
1. Click vào icon extension trên toolbar
2. Popup hiển thị với hình màu xanh đẹp
3. Đóng popup bằng cách click ra ngoài

### Nếu gặp lỗi:
- **"Manifest file is missing"**: Kiểm tra file `manifest.json` có tồn tại
- **"Could not load icon"**: Kiểm tra thư mục `icons/` và các file PNG
- **"Invalid manifest"**: Kiểm tra JSON syntax trong manifest.json

## 🔧 Troubleshooting

### Lỗi thường gặp:

1. **Extension không xuất hiện**
   - Kiểm tra Developer mode đã bật
   - Thử reload extension

2. **Icon không hiển thị**
   - Kiểm tra file icons có tồn tại
   - Kiểm tra đường dẫn trong manifest.json

3. **Popup không mở**
   - Kiểm tra file popup.html
   - Xem console errors (F12)

### Debug steps:
1. Vào `chrome://extensions/`
2. Tìm "Green Image Viewer"
3. Click "Details"
4. Click "Inspect views: popup" (nếu có)
5. Xem Console tab để tìm errors

## ✨ Thành công!

Nếu mọi thứ hoạt động đúng:
- Icon extension hiển thị trên toolbar
- Click vào icon sẽ mở popup với hình xanh đẹp
- Không có error messages

Extension đã sẵn sàng sử dụng! 🌿

---

**Lưu ý:** Extension này hoàn toàn an toàn, không yêu cầu permissions và không thu thập dữ liệu.
