# Green Theme Extension - Git Ignore File

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Build outputs
dist/
build/
*.zip
*.crx
*.pem

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
.cache/

# Logs
logs/
*.log

# Generated icons (if using auto-generation)
icons/templates/
icons/*.svg

# Development files
.env
.env.local
.env.development
.env.test
.env.production

# Chrome extension development
*.crx
*.pem
key.pem

# Backup files
*.bak
*.backup

# Test results
test-results/
coverage/
