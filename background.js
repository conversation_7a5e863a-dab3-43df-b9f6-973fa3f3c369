// Background Service Worker cho Green Theme Extension
// Xử lý logic chính: lưu trữ state, inject/remove CSS, quản lý tabs

// Khởi tạo extension
chrome.runtime.onInstalled.addListener(async () => {
  // Đặt trạng thái mặc định là OFF
  await chrome.storage.sync.set({ greenThemeEnabled: false });
  console.log('Green Theme Extension đã được cài đặt');
});

// Lắng nghe khi tab được tạo mới
chrome.tabs.onCreated.addListener(async (tab) => {
  const { greenThemeEnabled } = await chrome.storage.sync.get('greenThemeEnabled');
  if (greenThemeEnabled && isValidUrl(tab.url)) {
    // Đợi tab load xong rồi mới inject CSS
    chrome.tabs.onUpdated.addListener(function listener(tabId, info) {
      if (tabId === tab.id && info.status === 'complete') {
        chrome.tabs.onUpdated.removeListener(listener);
        injectGreenTheme(tabId);
      }
    });
  }
});

// Lắng nghe khi tab được cập nhật (reload, navigate)
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete') {
    const { greenThemeEnabled } = await chrome.storage.sync.get('greenThemeEnabled');
    if (greenThemeEnabled && isValidUrl(tab.url)) {
      injectGreenTheme(tabId);
    }
  }
});

// Lắng nghe messages từ popup
chrome.runtime.onMessage.addListener(async (request, sender, sendResponse) => {
  switch (request.action) {
    case 'getState':
      const { greenThemeEnabled } = await chrome.storage.sync.get('greenThemeEnabled');
      sendResponse({ enabled: greenThemeEnabled || false });
      break;
      
    case 'toggleTheme':
      await toggleGreenTheme();
      const { greenThemeEnabled: newState } = await chrome.storage.sync.get('greenThemeEnabled');
      sendResponse({ enabled: newState });
      break;
      
    case 'refreshAllTabs':
      await refreshAllTabs();
      sendResponse({ success: true });
      break;
  }
  return true; // Giữ message channel mở cho async response
});

// Hàm toggle theme chính
async function toggleGreenTheme() {
  const { greenThemeEnabled } = await chrome.storage.sync.get('greenThemeEnabled');
  const newState = !greenThemeEnabled;
  
  // Lưu state mới
  await chrome.storage.sync.set({ greenThemeEnabled: newState });
  
  // Cập nhật icon
  updateIcon(newState);
  
  // Áp dụng hoặc gỡ bỏ theme cho tất cả tabs
  const tabs = await chrome.tabs.query({});
  
  for (const tab of tabs) {
    if (isValidUrl(tab.url)) {
      if (newState) {
        await injectGreenTheme(tab.id);
      } else {
        await removeGreenTheme(tab.id);
      }
    }
  }
}

// Inject CSS vào tab
async function injectGreenTheme(tabId) {
  try {
    // Kiểm tra xem CSS đã được inject chưa để tránh duplicate
    const results = await chrome.scripting.executeScript({
      target: { tabId },
      func: () => document.querySelector('#green-theme-injected') !== null
    });
    
    if (results[0]?.result) {
      return; // CSS đã được inject rồi
    }
    
    // Inject CSS file
    await chrome.scripting.insertCSS({
      target: { tabId },
      files: ['styles/green.css']
    });
    
    // Đánh dấu đã inject để tránh duplicate
    await chrome.scripting.executeScript({
      target: { tabId },
      func: () => {
        const marker = document.createElement('meta');
        marker.id = 'green-theme-injected';
        marker.name = 'green-theme';
        marker.content = 'injected';
        document.head.appendChild(marker);
      }
    });
    
    console.log(`Green theme đã được áp dụng cho tab ${tabId}`);
  } catch (error) {
    console.error(`Lỗi khi inject CSS vào tab ${tabId}:`, error);
  }
}

// Gỡ bỏ CSS khỏi tab
async function removeGreenTheme(tabId) {
  try {
    // Gỡ bỏ CSS
    await chrome.scripting.removeCSS({
      target: { tabId },
      files: ['styles/green.css']
    });
    
    // Gỡ bỏ marker
    await chrome.scripting.executeScript({
      target: { tabId },
      func: () => {
        const marker = document.querySelector('#green-theme-injected');
        if (marker) {
          marker.remove();
        }
      }
    });
    
    console.log(`Green theme đã được gỡ bỏ khỏi tab ${tabId}`);
  } catch (error) {
    console.error(`Lỗi khi gỡ bỏ CSS khỏi tab ${tabId}:`, error);
  }
}

// Cập nhật icon dựa trên state
function updateIcon(enabled) {
  const iconPath = enabled ? {
    "16": "icons/icon16-active.png",
    "32": "icons/icon32-active.png", 
    "48": "icons/icon48-active.png",
    "128": "icons/icon128-active.png"
  } : {
    "16": "icons/icon16.png",
    "32": "icons/icon32.png",
    "48": "icons/icon48.png", 
    "128": "icons/icon128.png"
  };
  
  chrome.action.setIcon({ path: iconPath });
}

// Kiểm tra URL có hợp lệ không (loại bỏ chrome://, edge://, extension://)
function isValidUrl(url) {
  if (!url) return false;
  
  const invalidProtocols = [
    'chrome://',
    'chrome-extension://',
    'edge://',
    'moz-extension://',
    'about:',
    'data:',
    'file://'
  ];
  
  return !invalidProtocols.some(protocol => url.startsWith(protocol));
}

// Làm mới tất cả tabs (áp dụng lại theme)
async function refreshAllTabs() {
  const { greenThemeEnabled } = await chrome.storage.sync.get('greenThemeEnabled');
  
  if (!greenThemeEnabled) return;
  
  const tabs = await chrome.tabs.query({});
  
  for (const tab of tabs) {
    if (isValidUrl(tab.url)) {
      // Gỡ bỏ rồi inject lại để đảm bảo CSS được áp dụng đúng
      await removeGreenTheme(tab.id);
      await injectGreenTheme(tab.id);
    }
  }
}
