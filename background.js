// Background Service Worker cho Green Theme Extension
// Xử lý logic chính: lưu trữ state, inject/remove CSS, quản lý tabs

// Khởi tạo extension
chrome.runtime.onInstalled.addListener(async () => {
  // Đặt trạng thái mặc định là OFF
  await chrome.storage.sync.set({ greenThemeEnabled: false });
  console.log('Green Theme Extension đã được cài đặt');
});

// Lắng nghe khi tab được tạo mới
chrome.tabs.onCreated.addListener(async (tab) => {
  const { greenThemeEnabled } = await chrome.storage.sync.get('greenThemeEnabled');
  if (greenThemeEnabled && isValidUrl(tab.url)) {
    // Đợi tab load xong rồi mới inject CSS
    chrome.tabs.onUpdated.addListener(function listener(tabId, info) {
      if (tabId === tab.id && info.status === 'complete') {
        chrome.tabs.onUpdated.removeListener(listener);
        injectGreenTheme(tabId);
      }
    });
  }
});

// Lắng nghe khi tab được cập nhật (reload, navigate)
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete') {
    const { greenThemeEnabled } = await chrome.storage.sync.get('greenThemeEnabled');
    if (greenThemeEnabled && isValidUrl(tab.url)) {
      injectGreenTheme(tabId);
    }
  }
});

// Lắng nghe messages từ popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  (async () => {
    try {
      switch (request.action) {
        case 'getState':
          const { greenThemeEnabled } = await chrome.storage.sync.get('greenThemeEnabled');
          sendResponse({ enabled: greenThemeEnabled || false });
          break;

        case 'toggleTheme':
          await toggleGreenTheme();
          const { greenThemeEnabled: newState } = await chrome.storage.sync.get('greenThemeEnabled');
          sendResponse({ enabled: newState });
          break;

        case 'refreshAllTabs':
          await refreshAllTabs();
          sendResponse({ success: true });
          break;

        default:
          sendResponse({ error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Error in message handler:', error);
      sendResponse({ error: error.message });
    }
  })();
  return true; // Giữ message channel mở cho async response
});

// Hàm toggle theme chính
async function toggleGreenTheme() {
  const { greenThemeEnabled } = await chrome.storage.sync.get('greenThemeEnabled');
  const newState = !greenThemeEnabled;
  
  // Lưu state mới
  await chrome.storage.sync.set({ greenThemeEnabled: newState });
  
  // Cập nhật icon
  updateIcon(newState);
  
  // Áp dụng hoặc gỡ bỏ theme cho tất cả tabs
  const tabs = await chrome.tabs.query({});
  
  for (const tab of tabs) {
    if (isValidUrl(tab.url)) {
      if (newState) {
        await injectGreenTheme(tab.id);
      } else {
        await removeGreenTheme(tab.id);
      }
    }
  }
}

// Inject CSS vào tab
async function injectGreenTheme(tabId) {
  try {
    // Lấy thông tin tab trước
    const tab = await chrome.tabs.get(tabId);
    if (!isValidUrl(tab.url)) {
      console.log(`Bỏ qua tab ${tabId}: URL không hợp lệ (${tab.url})`);
      return;
    }

    // Kiểm tra xem CSS đã được inject chưa để tránh duplicate
    const results = await chrome.scripting.executeScript({
      target: { tabId },
      func: () => document.querySelector('#green-theme-injected') !== null
    });

    if (results[0]?.result) {
      console.log(`Tab ${tabId} đã có green theme`);
      return; // CSS đã được inject rồi
    }

    // Inject CSS file
    await chrome.scripting.insertCSS({
      target: { tabId },
      files: ['styles/green.css']
    });

    // Đánh dấu đã inject để tránh duplicate
    await chrome.scripting.executeScript({
      target: { tabId },
      func: () => {
        const marker = document.createElement('meta');
        marker.id = 'green-theme-injected';
        marker.name = 'green-theme';
        marker.content = 'injected';
        document.head.appendChild(marker);
      }
    });

    console.log(`Green theme đã được áp dụng cho tab ${tabId}`);
  } catch (error) {
    if (error.message.includes('Cannot access contents')) {
      console.log(`Không thể truy cập tab ${tabId}: Trang được bảo vệ`);
    } else if (error.message.includes('cannot be scripted')) {
      console.log(`Không thể script tab ${tabId}: Trang extension/webstore`);
    } else {
      console.error(`Lỗi khi inject CSS vào tab ${tabId}:`, error.message);
    }
  }
}

// Gỡ bỏ CSS khỏi tab
async function removeGreenTheme(tabId) {
  try {
    // Lấy thông tin tab trước
    const tab = await chrome.tabs.get(tabId);
    if (!isValidUrl(tab.url)) {
      console.log(`Bỏ qua tab ${tabId}: URL không hợp lệ (${tab.url})`);
      return;
    }

    // Gỡ bỏ CSS
    await chrome.scripting.removeCSS({
      target: { tabId },
      files: ['styles/green.css']
    });

    // Gỡ bỏ marker
    await chrome.scripting.executeScript({
      target: { tabId },
      func: () => {
        const marker = document.querySelector('#green-theme-injected');
        if (marker) {
          marker.remove();
        }
      }
    });

    console.log(`Green theme đã được gỡ bỏ khỏi tab ${tabId}`);
  } catch (error) {
    if (error.message.includes('Cannot access contents')) {
      console.log(`Không thể truy cập tab ${tabId}: Trang được bảo vệ`);
    } else if (error.message.includes('cannot be scripted')) {
      console.log(`Không thể script tab ${tabId}: Trang extension/webstore`);
    } else if (error.message.includes('No tab with id')) {
      console.log(`Tab ${tabId} đã bị đóng`);
    } else {
      console.error(`Lỗi khi gỡ bỏ CSS khỏi tab ${tabId}:`, error.message);
    }
  }
}

// Cập nhật icon dựa trên state
async function updateIcon(enabled) {
  try {
    const iconPath = enabled ? {
      "16": "icons/icon16-active.png",
      "32": "icons/icon32-active.png",
      "48": "icons/icon48-active.png",
      "128": "icons/icon128-active.png"
    } : {
      "16": "icons/icon16.png",
      "32": "icons/icon32.png",
      "48": "icons/icon48.png",
      "128": "icons/icon128.png"
    };

    await chrome.action.setIcon({ path: iconPath });
  } catch (error) {
    console.warn('Không thể cập nhật icon:', error.message);
    // Fallback: chỉ thay đổi badge text
    chrome.action.setBadgeText({ text: enabled ? 'ON' : '' });
    chrome.action.setBadgeBackgroundColor({ color: enabled ? '#4CAF50' : '#666' });
  }
}

// Kiểm tra URL có hợp lệ không (loại bỏ chrome://, edge://, extension://)
function isValidUrl(url) {
  if (!url) return false;

  const invalidProtocols = [
    'chrome://',
    'chrome-extension://',
    'chrome-search://',
    'chrome-devtools://',
    'edge://',
    'moz-extension://',
    'about:',
    'data:',
    'file://',
    'ftp://',
    'view-source:'
  ];

  // Kiểm tra các trang đặc biệt
  const invalidPages = [
    'chrome.google.com/webstore',
    'microsoftedge.microsoft.com/addons',
    'addons.mozilla.org'
  ];

  // Kiểm tra protocol không hợp lệ
  if (invalidProtocols.some(protocol => url.startsWith(protocol))) {
    return false;
  }

  // Kiểm tra trang đặc biệt
  if (invalidPages.some(page => url.includes(page))) {
    return false;
  }

  // Chỉ cho phép http và https
  return url.startsWith('http://') || url.startsWith('https://');
}

// Làm mới tất cả tabs (áp dụng lại theme)
async function refreshAllTabs() {
  const { greenThemeEnabled } = await chrome.storage.sync.get('greenThemeEnabled');
  
  if (!greenThemeEnabled) return;
  
  const tabs = await chrome.tabs.query({});
  
  for (const tab of tabs) {
    if (isValidUrl(tab.url)) {
      // Gỡ bỏ rồi inject lại để đảm bảo CSS được áp dụng đúng
      await removeGreenTheme(tab.id);
      await injectGreenTheme(tab.id);
    }
  }
}
