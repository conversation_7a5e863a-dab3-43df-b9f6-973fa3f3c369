/* Green Theme CSS - Theme xanh dịu mắt cho tất cả websites */
/* <PERSON><PERSON><PERSON> bảo contrast tốt và không phá vỡ layout */

/* Reset và base styles */
html, body {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 50%, #e0f2e0 100%) !important;
  color: #1a4d1a !important;
  transition: background-color 0.3s ease, color 0.3s ease !important;
}

/* Wrapper containers */
body > div,
body > main,
body > section,
body > article,
.container,
.wrapper,
.content {
  background: rgba(240, 248, 240, 0.9) !important;
}

/* Text elements */
h1, h2, h3, h4, h5, h6 {
  color: #0d3d0d !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
}

p, span, div, li, td, th {
  color: #1a4d1a !important;
}

/* Links */
a {
  color: #0a5a0a !important;
  text-decoration: underline !important;
  transition: color 0.2s ease !important;
}

a:hover {
  color: #083308 !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.6) !important;
}

a:visited {
  color: #2d5a2d !important;
}

/* Buttons */
button, 
input[type="button"], 
input[type="submit"], 
input[type="reset"],
.btn,
.button {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%) !important;
  color: white !important;
  border: 1px solid #3d8b40 !important;
  transition: all 0.2s ease !important;
}

button:hover,
input[type="button"]:hover,
input[type="submit"]:hover,
input[type="reset"]:hover,
.btn:hover,
.button:hover {
  background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%) !important;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3) !important;
}

/* Form elements */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
input[type="url"],
textarea,
select {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 2px solid #c8e6c9 !important;
  color: #1a4d1a !important;
  transition: border-color 0.2s ease !important;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="search"]:focus,
input[type="url"]:focus,
textarea:focus,
select:focus {
  border-color: #4CAF50 !important;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1) !important;
  outline: none !important;
}

/* Tables */
table {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid #c8e6c9 !important;
}

th {
  background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%) !important;
  color: #0d3d0d !important;
  border-bottom: 2px solid #4CAF50 !important;
}

td {
  border-bottom: 1px solid #e8f5e8 !important;
}

tr:nth-child(even) {
  background: rgba(232, 245, 232, 0.5) !important;
}

tr:hover {
  background: rgba(200, 230, 201, 0.3) !important;
}

/* Cards và panels */
.card,
.panel,
.box,
.widget,
article {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid #c8e6c9 !important;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1) !important;
}

/* Navigation */
nav,
.navbar,
.navigation,
.menu {
  background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%) !important;
  border-bottom: 3px solid #1b5e20 !important;
}

nav a,
.navbar a,
.navigation a,
.menu a {
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

nav a:hover,
.navbar a:hover,
.navigation a:hover,
.menu a:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #e8f5e8 !important;
}

/* Headers và footers */
header,
.header {
  background: linear-gradient(135deg, #1b5e20 0%, #2e7d32 100%) !important;
  color: white !important;
}

footer,
.footer {
  background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%) !important;
  color: #e8f5e8 !important;
  border-top: 3px solid #4CAF50 !important;
}

/* Sidebar */
.sidebar,
.side-nav,
aside {
  background: rgba(232, 245, 232, 0.95) !important;
  border-right: 2px solid #c8e6c9 !important;
}

/* Code blocks */
code,
pre,
.code,
.highlight {
  background: rgba(27, 94, 32, 0.1) !important;
  color: #1b5e20 !important;
  border: 1px solid #c8e6c9 !important;
  border-left: 4px solid #4CAF50 !important;
}

/* Blockquotes */
blockquote {
  background: rgba(232, 245, 232, 0.8) !important;
  border-left: 4px solid #4CAF50 !important;
  color: #1a4d1a !important;
}

/* Scrollbars (Webkit) */
::-webkit-scrollbar {
  width: 12px !important;
  height: 12px !important;
}

::-webkit-scrollbar-track {
  background: #e8f5e8 !important;
  border-radius: 6px !important;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%) !important;
  border-radius: 6px !important;
  border: 2px solid #e8f5e8 !important;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%) !important;
}

/* Selection */
::selection {
  background: rgba(76, 175, 80, 0.3) !important;
  color: #0d3d0d !important;
}

::-moz-selection {
  background: rgba(76, 175, 80, 0.3) !important;
  color: #0d3d0d !important;
}

/* Dark mode compatibility */
@media (prefers-color-scheme: dark) {
  html, body {
    background: linear-gradient(135deg, #1a3d1a 0%, #2d4d2d 50%, #1a4d1a 100%) !important;
    color: #c8e6c9 !important;
  }
  
  h1, h2, h3, h4, h5, h6 {
    color: #e8f5e8 !important;
  }
  
  p, span, div, li, td, th {
    color: #c8e6c9 !important;
  }
  
  a {
    color: #81c784 !important;
  }
  
  a:hover {
    color: #a5d6a7 !important;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  body {
    font-size: 16px !important;
    line-height: 1.6 !important;
  }
}

/* Animation cho smooth transitions */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
}

/* Đảm bảo không ảnh hưởng đến các element quan trọng */
iframe,
embed,
object,
video,
canvas,
svg {
  filter: none !important;
}

/* Fix cho các trang có background images */
body[style*="background-image"] {
  background-blend-mode: overlay !important;
}
