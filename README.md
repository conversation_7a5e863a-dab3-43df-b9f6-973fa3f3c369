# Green Theme Extension

Chrome Extension (Manifest V3) áp dụng theme xanh dịu mắt cho tất cả các trang web.

## ✨ Tính năng

- 🌿 **Green Theme**: Áp dụng theme xanh dịu mắt, tương phản tốt
- 🔄 **Toggle dễ dàng**: Bật/tắt qua popup hoặc action button
- 💾 **Lưu trạng thái**: Tự động khôi phục khi khởi động lại trình duyệt
- 🚀 **Auto-apply**: Tab mới tự động áp dụng theme khi đã bật
- 🛡️ **An toàn**: Không ảnh hưởng trang chrome:// và extension://
- 🎨 **Không phá layout**: Chỉ thay đổi màu sắc, giữ nguyên cấu trúc
- 📱 **Responsive**: Hỗ trợ cả desktop và mobile view

## 🏗️ Cấu trúc Project

```
green-theme-extension/
├── manifest.json              # Cấu hình extension
├── background.js              # Service worker chính
├── popup.html                 # Giao diện popup
├── popup.js                   # Logic popup
├── contentScript.js           # Content script hỗ trợ
├── styles/
│   └── green.css             # CSS theme xanh
├── icons/
│   ├── README.md             # Hướng dẫn tạo icons
│   ├── icon16.png            # Icon 16x16 (cần tạo)
│   ├── icon32.png            # Icon 32x32 (cần tạo)
│   ├── icon48.png            # Icon 48x48 (cần tạo)
│   ├── icon128.png           # Icon 128x128 (cần tạo)
│   ├── icon16-active.png     # Icon active 16x16 (cần tạo)
│   ├── icon32-active.png     # Icon active 32x32 (cần tạo)
│   ├── icon48-active.png     # Icon active 48x48 (cần tạo)
│   └── icon128-active.png    # Icon active 128x128 (cần tạo)
├── test/
│   └── manual-testing-guide.md # Hướng dẫn test thủ công
└── README.md                  # File này
```

## 🚀 Hướng dẫn cài đặt

### Chuẩn bị Icons (Bắt buộc)

Trước khi cài đặt, bạn cần tạo các file icon. Xem hướng dẫn chi tiết trong `icons/README.md`.

**Tạo nhanh icons đơn giản:**
1. Tạo hình vuông 128x128px, nền xám (#E0E0E0), chữ "G" màu xám đậm
2. Tạo phiên bản active: nền xanh (#4CAF50), chữ "G" màu trắng
3. Resize thành các kích thước: 16px, 32px, 48px, 128px
4. Lưu vào thư mục `icons/` với đúng tên file

### Cài đặt Extension

1. **Mở Chrome/Edge**
2. **Vào trang Extensions:**
   - Chrome: `chrome://extensions/`
   - Edge: `edge://extensions/`
3. **Bật Developer Mode** (góc trên bên phải)
4. **Click "Load unpacked"**
5. **Chọn thư mục** chứa extension này
6. **Kiểm tra** extension xuất hiện trong danh sách

### Sử dụng

1. **Click vào icon extension** trên toolbar
2. **Toggle switch** để bật/tắt Green Theme
3. **Nút "Làm mới tất cả tab"** để re-apply theme nếu cần

## 🛠️ Phát triển

### Yêu cầu hệ thống
- Chrome 88+ hoặc Edge 88+
- Manifest V3 support

### Cấu trúc code
- **background.js**: Service worker xử lý logic chính
- **popup.js**: Giao diện người dùng
- **styles/green.css**: Theme CSS với contrast tốt
- **contentScript.js**: Hỗ trợ và monitoring

### Debugging
1. **Background script**: `chrome://extensions/` → Details → Inspect views
2. **Popup**: Right-click popup → Inspect
3. **Content script**: F12 trên trang web → Console

## 🧪 Testing

Xem hướng dẫn test chi tiết trong `test/manual-testing-guide.md`.

**Test nhanh:**
1. Bật/tắt theme và kiểm tra thay đổi
2. Mở tab mới khi theme đang bật
3. Reload trang và kiểm tra theme
4. Khởi động lại browser và kiểm tra state

## 🎨 Tùy chỉnh

### Thay đổi màu theme
Chỉnh sửa file `styles/green.css`:
- Màu nền chính: `#e8f5e8`
- Màu text: `#1a4d1a`
- Màu accent: `#4CAF50`

### Thêm theme mới
1. Tạo file CSS mới trong `styles/`
2. Thêm logic toggle trong `background.js`
3. Cập nhật popup UI

## 📋 Roadmap

- [ ] **Multi-theme support**: Dark theme, Blue theme, etc.
- [ ] **Custom colors**: Cho phép user chọn màu
- [ ] **Website whitelist**: Loại trừ một số trang
- [ ] **Intensity control**: Điều chỉnh độ đậm nhạt
- [ ] **Keyboard shortcuts**: Hotkey toggle
- [ ] **Sync settings**: Đồng bộ qua Chrome account

## 🐛 Báo lỗi

Nếu gặp lỗi, vui lòng cung cấp:
1. **Bước tái tạo** chi tiết
2. **Chrome/Edge version**
3. **Console errors** (F12 → Console)
4. **Website** gặp lỗi (nếu có)

## 📄 License

MIT License - Tự do sử dụng và chỉnh sửa.

## 🤝 Đóng góp

1. Fork project
2. Tạo feature branch
3. Commit changes
4. Push và tạo Pull Request

---

**Green Theme Extension v1.0.0** - Làm cho web xanh và dịu mắt hơn! 🌿
