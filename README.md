# Green Image Viewer

Chrome Extension (Manifest V3) đơn giản hiển thị hình ảnh màu xanh đẹp mắt.

## ✨ Tính năng

- 🌿 **Hình ảnh xanh**: Hiển thị gradient màu xanh đẹp mắt
- 🎨 **Giao diện đẹp**: Popup với thiết kế hiện đại
- 🚀 **Đơn giản**: Chỉ cần click vào icon extension
- 🛡️ **An toàn**: Không yêu cầu bất kỳ quyền nào
- 📱 **Responsive**: Giao diện thân thiện

## 🏗️ Cấu trúc Project

```
green-image-viewer/
├── manifest.json              # Cấu hình extension (không có permissions)
├── popup.html                 # Giao diện popup hiển thị hình xanh
└── README.md                  # File này
```

## 🚀 Hướng dẫn cài đặt

### Cài đặt Extension

1. **Mở Chrome/Edge**
2. **Vào trang Extensions:**
   - Chrome: `chrome://extensions/`
   - Edge: `edge://extensions/`
3. **Bật Developer Mode** (góc trên bên phải)
4. **Click "Load unpacked"**
5. **Chọn thư mục** chứa extension này
6. **Kiểm tra** extension xuất hiện trong danh sách

### Sử dụng

1. **Click vào icon extension** trên toolbar
2. **Thưởng thức** hình ảnh màu xanh đẹp mắt
3. **Đóng popup** khi muốn

## 🛠️ Đặc điểm kỹ thuật

### Yêu cầu hệ thống
- Chrome 88+ hoặc Edge 88+
- Manifest V3 support

### Không yêu cầu permissions
- ✅ Không cần quyền truy cập tabs
- ✅ Không cần quyền truy cập storage
- ✅ Không cần quyền truy cập scripting
- ✅ Không cần quyền truy cập host
- ✅ Hoàn toàn an toàn và riêng tư

### Cấu trúc code
- **manifest.json**: Chỉ có action popup, không có permissions
- **popup.html**: HTML + CSS thuần, không có JavaScript

## 🎨 Tùy chỉnh

### Thay đổi màu sắc
Chỉnh sửa CSS trong `popup.html`:
```css
background: linear-gradient(45deg,
  #4CAF50 0%,
  #66BB6A 25%,
  #81C784 50%,
  #A5D6A7 75%,
  #C8E6C9 100%);
```

### Thay đổi kích thước
```css
.green-image {
  width: 300px;  /* Thay đổi chiều rộng */
  height: 200px; /* Thay đổi chiều cao */
}
```

## 📋 Roadmap

- [ ] **Nhiều màu sắc**: Blue, Purple, Orange images
- [ ] **Animation**: Thêm hiệu ứng chuyển động
- [ ] **Random images**: Hiển thị ngẫu nhiên các hình khác nhau
- [ ] **Download**: Cho phép tải hình về

## 🐛 Báo lỗi

Extension này rất đơn giản nên ít có lỗi. Nếu gặp vấn đề:
1. **Kiểm tra** manifest.json syntax
2. **Reload** extension trong chrome://extensions/
3. **Kiểm tra** popup.html hiển thị đúng

## 📄 License

MIT License - Tự do sử dụng và chỉnh sửa.

## 🤝 Đóng góp

1. Fork project
2. Tạo feature branch
3. Commit changes
4. Push và tạo Pull Request

---

**Green Image Viewer v1.0.0** - Đơn giản và đẹp mắt! 🌿
