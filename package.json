{"name": "green-image-viewer", "version": "1.0.0", "description": "Chrome Extension hiển thị hình ảnh màu xanh đẹp mắt", "main": "popup.html", "scripts": {"build": "echo 'Extension ready for packaging'", "test": "echo 'Run manual tests in test/ directory'", "package": "zip -r green-theme-extension.zip . -x '*.git*' 'node_modules/*' 'package*.json' '.DS_Store'", "dev": "echo 'Load unpacked extension in Chrome Developer Mode'", "icons": "node scripts/generate-icons.js"}, "keywords": ["chrome-extension", "image-viewer", "green", "simple", "no-permissions", "manifest-v3"], "author": "Green Image Viewer Developer", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/green-image-viewer.git"}, "bugs": {"url": "https://github.com/your-username/green-image-viewer/issues"}, "homepage": "https://github.com/your-username/green-image-viewer#readme", "devDependencies": {}, "engines": {"node": ">=14.0.0"}, "browserslist": ["Chrome >= 88", "Edge >= 88"]}