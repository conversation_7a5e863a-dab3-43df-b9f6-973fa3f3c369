{"name": "green-theme-extension", "version": "1.0.0", "description": "Chrome Extension áp dụng theme xanh dịu mắt cho tất cả các trang web", "main": "background.js", "scripts": {"build": "echo 'Extension ready for packaging'", "test": "echo 'Run manual tests in test/ directory'", "package": "zip -r green-theme-extension.zip . -x '*.git*' 'node_modules/*' 'package*.json' '.DS_Store'", "dev": "echo 'Load unpacked extension in Chrome Developer Mode'", "icons": "node scripts/generate-icons.js"}, "keywords": ["chrome-extension", "theme", "green", "dark-mode", "accessibility", "manifest-v3"], "author": "Green Theme Extension Developer", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/green-theme-extension.git"}, "bugs": {"url": "https://github.com/your-username/green-theme-extension/issues"}, "homepage": "https://github.com/your-username/green-theme-extension#readme", "devDependencies": {}, "engines": {"node": ">=14.0.0"}, "browserslist": ["Chrome >= 88", "Edge >= 88"]}