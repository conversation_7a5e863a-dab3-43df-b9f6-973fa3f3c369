// Popup Script cho Green Theme Extension
// Xử lý giao diện popup và tương tác với background script

document.addEventListener('DOMContentLoaded', async () => {
  // Lấy các elements
  const themeToggle = document.getElementById('themeToggle');
  const statusDiv = document.getElementById('status');
  const statusText = document.getElementById('statusText');
  const refreshBtn = document.getElementById('refreshBtn');
  const loading = document.getElementById('loading');

  // Khởi tạo popup
  await initializePopup();

  // Event listeners
  themeToggle.addEventListener('click', handleToggleClick);
  refreshBtn.addEventListener('click', handleRefreshClick);

  // Khởi tạo trạng thái popup
  async function initializePopup() {
    try {
      // Lấy trạng thái hiện tại từ background
      const response = await sendMessageToBackground('getState');
      updateUI(response.enabled);
    } catch (error) {
      console.error('Lỗi khi khởi tạo popup:', error);
      showError('Không thể tải trạng thái extension');
    }
  }

  // Xử lý click toggle
  async function handleToggleClick() {
    try {
      showLoading(true);
      
      // Gửi lệnh toggle đến background
      const response = await sendMessageToBackground('toggleTheme');
      updateUI(response.enabled);
      
      // Hiển thị thông báo ngắn
      showTemporaryMessage(response.enabled ? 'Đã bật Green Theme!' : 'Đã tắt Green Theme!');
      
    } catch (error) {
      console.error('Lỗi khi toggle theme:', error);
      showError('Không thể thay đổi theme');
    } finally {
      showLoading(false);
    }
  }

  // Xử lý click refresh
  async function handleRefreshClick() {
    try {
      showLoading(true);
      refreshBtn.disabled = true;
      refreshBtn.textContent = '🔄 Đang làm mới...';
      
      // Gửi lệnh refresh đến background
      await sendMessageToBackground('refreshAllTabs');
      
      showTemporaryMessage('Đã làm mới tất cả tab!');
      
    } catch (error) {
      console.error('Lỗi khi refresh tabs:', error);
      showError('Không thể làm mới tabs');
    } finally {
      showLoading(false);
      refreshBtn.disabled = false;
      refreshBtn.textContent = '🔄 Làm mới tất cả tab';
    }
  }

  // Cập nhật giao diện dựa trên trạng thái
  function updateUI(enabled) {
    // Cập nhật toggle switch
    if (enabled) {
      themeToggle.classList.add('active');
    } else {
      themeToggle.classList.remove('active');
    }

    // Cập nhật status
    statusDiv.className = enabled ? 'status enabled' : 'status disabled';
    statusText.textContent = enabled ? 
      '✅ Green Theme đang hoạt động' : 
      '❌ Green Theme đã tắt';

    // Cập nhật refresh button
    refreshBtn.style.display = enabled ? 'block' : 'none';
  }

  // Gửi message đến background script
  function sendMessageToBackground(action, data = {}) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({ action, ...data }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });
  }

  // Hiển thị/ẩn loading
  function showLoading(show) {
    loading.style.display = show ? 'block' : 'none';
  }

  // Hiển thị lỗi
  function showError(message) {
    statusDiv.className = 'status disabled';
    statusText.textContent = `❌ ${message}`;
  }

  // Hiển thị thông báo tạm thời
  function showTemporaryMessage(message) {
    const originalText = statusText.textContent;
    const originalClass = statusDiv.className;
    
    statusDiv.className = 'status enabled';
    statusText.textContent = message;
    
    setTimeout(() => {
      statusDiv.className = originalClass;
      statusText.textContent = originalText;
    }, 2000);
  }

  // Lắng nghe thay đổi storage để cập nhật UI real-time
  chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'sync' && changes.greenThemeEnabled) {
      updateUI(changes.greenThemeEnabled.newValue);
    }
  });
});
