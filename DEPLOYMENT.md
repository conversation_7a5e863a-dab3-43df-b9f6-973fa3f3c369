# Hướng dẫn Đóng gói & <PERSON><PERSON>t hành Extension

## 🚀 Hướng dẫn Đóng gói Nhanh

### Bước 1: <PERSON><PERSON>n bị Icons
```bash
# Tạo icons tự động (nếu có Node.js)
node scripts/generate-icons.js

# Hoặc tạo thủ công theo hướng dẫn trong icons/README.md
```

### Bước 2: Ki<PERSON>m tra Files
Đảm bảo có đầy đủ các files:
```
✅ manifest.json
✅ background.js
✅ popup.html
✅ popup.js
✅ contentScript.js
✅ styles/green.css
✅ icons/icon16.png
✅ icons/icon32.png
✅ icons/icon48.png
✅ icons/icon128.png
✅ icons/icon16-active.png
✅ icons/icon32-active.png
✅ icons/icon48-active.png
✅ icons/icon128-active.png
```

### Bước 3: Test Extension
1. Load unpacked trong Chrome Developer Mode
2. Chạy test cases trong `test/manual-testing-guide.md`
3. Kiểm tra console không có errors

### Bước 4: Đ<PERSON>g gói
```bash
# Tạo ZIP file (Linux/Mac)
zip -r green-theme-extension.zip . -x '*.git*' 'node_modules/*' 'package*.json' '.DS_Store' 'scripts/*' 'test/*'

# Hoặc thủ công:
# 1. Tạo thư mục mới: green-theme-extension-release/
# 2. Copy các files cần thiết (không copy scripts/, test/, .git)
# 3. Nén thành ZIP
```

## 📦 Cài đặt Developer Mode

### Chrome
1. Mở `chrome://extensions/`
2. Bật "Developer mode" (góc trên phải)
3. Click "Load unpacked"
4. Chọn thư mục extension
5. Extension sẽ xuất hiện trong danh sách

### Edge
1. Mở `edge://extensions/`
2. Bật "Developer mode" (sidebar trái)
3. Click "Load unpacked"
4. Chọn thư mục extension
5. Extension sẽ xuất hiện trong danh sách

## 🏪 Phát hành Chrome Web Store

### Chuẩn bị
1. **Tài khoản Developer**: Đăng ký tại [Chrome Web Store Developer Dashboard](https://chrome.google.com/webstore/devconsole/)
2. **Phí đăng ký**: $5 USD (một lần)
3. **Tài liệu**: Chuẩn bị mô tả, screenshots, privacy policy

### Bước phát hành
1. **Tạo ZIP package** (như hướng dẫn trên)
2. **Upload lên Developer Dashboard**
3. **Điền thông tin**:
   - Name: "Green Theme Extension"
   - Description: "Áp dụng theme xanh dịu mắt cho tất cả các trang web"
   - Category: "Accessibility" hoặc "Productivity"
   - Screenshots: Chụp popup và website với theme
4. **Privacy Policy** (nếu cần)
5. **Submit for review**

### Thông tin cần thiết
```
Name: Green Theme Extension
Short description: Theme xanh dịu mắt cho web
Detailed description: 
Extension này áp dụng theme màu xanh dịu mắt cho tất cả các trang web, 
giúp giảm mỏi mắt và tạo trải nghiệm duyệt web thoải mái hơn. 
Dễ dàng bật/tắt qua popup, tự động lưu trạng thái.

Category: Accessibility
Language: Vietnamese (vi)
```

## 🔧 Phát hành Edge Add-ons

### Chuẩn bị
1. **Tài khoản**: Đăng ký tại [Microsoft Partner Center](https://partner.microsoft.com/)
2. **Miễn phí**: Không tốn phí đăng ký
3. **Tương tự Chrome**: Cùng package ZIP

### Bước phát hành
1. Upload ZIP file
2. Điền thông tin tương tự Chrome
3. Submit for review

## 📋 Checklist trước khi phát hành

### Code Quality
- [ ] Tất cả functions có comments tiếng Việt
- [ ] Không có console.log trong production
- [ ] Error handling đầy đủ
- [ ] Performance tối ưu

### Functionality
- [ ] Toggle theme hoạt động đúng
- [ ] State persistence hoạt động
- [ ] Auto-apply cho tab mới
- [ ] Graceful handling cho chrome:// pages
- [ ] Icons switching hoạt động

### UI/UX
- [ ] Popup hiển thị đẹp
- [ ] Loading states rõ ràng
- [ ] Error messages hữu ích
- [ ] Responsive design

### Compatibility
- [ ] Test trên Chrome 88+
- [ ] Test trên Edge 88+
- [ ] Test trên Windows/Mac/Linux
- [ ] Test với các website phổ biến

### Documentation
- [ ] README.md đầy đủ
- [ ] Test guide hoàn chỉnh
- [ ] Icons có sẵn
- [ ] Comments code rõ ràng

## 🔄 Cập nhật Extension

### Cập nhật Local (Developer Mode)
1. Sửa code
2. Vào `chrome://extensions/`
3. Click nút "Reload" trên extension

### Cập nhật Store
1. Tăng version trong `manifest.json`
2. Tạo package mới
3. Upload lên Developer Dashboard
4. Submit for review

### Version Numbering
- **1.0.0**: Release đầu tiên
- **1.0.1**: Bug fixes
- **1.1.0**: Tính năng mới nhỏ
- **2.0.0**: Thay đổi lớn

## 🐛 Troubleshooting

### Extension không load
- Kiểm tra manifest.json syntax
- Kiểm tra tất cả files có tồn tại
- Xem console errors

### Icons không hiển thị
- Kiểm tra file paths trong manifest.json
- Đảm bảo icons có đúng kích thước
- Kiểm tra file format (PNG recommended)

### CSS không áp dụng
- Kiểm tra permissions trong manifest.json
- Kiểm tra host_permissions
- Xem console errors trong background script

### Store rejection
- Đọc kỹ feedback từ reviewer
- Kiểm tra policy violations
- Cập nhật và submit lại

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra console errors (F12)
2. Xem background script logs
3. Tham khảo [Chrome Extension Documentation](https://developer.chrome.com/docs/extensions/)
4. Tạo issue trên GitHub repository

---

**Chúc bạn thành công với Green Theme Extension! 🌿**
