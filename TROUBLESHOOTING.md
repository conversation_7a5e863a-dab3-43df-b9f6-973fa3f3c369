# 🔧 Khắc phục Lỗi - Green Theme Extension

## ✅ Các lỗi đã được sửa

### 1. "The message port closed before a response was received"
**Nguyên nhân:** Background script không xử lý async messages đúng cách  
**Đ<PERSON> sửa:** C<PERSON>i thiện message handler v<PERSON><PERSON> try-catch và proper async handling

### 2. "Failed to set icon: Failed to fetch"
**<PERSON>uyê<PERSON> nhân:** File icons không tồn tại  
**Đ<PERSON> sửa:** Thêm fallback với badge text khi không load được icons

### 3. "Cannot access contents of the page"
**Nguyên nhân:** Thiếu permissions hoặc trang được bảo vệ  
**Đ<PERSON> sửa:** 
- Thêm `activeTab` permission
- Cải thiện `host_permissions`
- Better error handling cho protected pages

### 4. "The extensions gallery cannot be scripted"
**Nguyên nhân:** <PERSON><PERSON> gắ<PERSON> inject CSS vào trang chrome://extensions/  
**<PERSON><PERSON> sửa:** <PERSON><PERSON><PERSON> thiện `isValidUrl()` function để loại bỏ tất cả trang đặc biệt

## 🚀 Hướng dẫn cài đặt nhanh

### Bước 1: Tạo Icons (Quan trọng!)
```bash
# Mở file này trong browser:
icons/create-simple-icons.html

# Hoặc chạy script:
node scripts/generate-icons.js
```

### Bước 2: Load Extension
1. Mở `chrome://extensions/`
2. Bật "Developer mode"
3. Click "Load unpacked"
4. Chọn thư mục này

### Bước 3: Test
- Click icon extension trên toolbar
- Toggle ON/OFF
- Mở tab mới và kiểm tra

## 🐛 Nếu vẫn gặp lỗi

### Lỗi: "Extension icon not found"
**Giải pháp:**
1. Đảm bảo có file icons trong thư mục `icons/`
2. Hoặc tạm thời comment out icon paths trong `manifest.json`

### Lỗi: "Cannot access chrome:// pages"
**Bình thường:** Extension không thể truy cập trang chrome://, edge://, etc.  
**Không cần sửa:** Đây là hành vi mong muốn

### Lỗi: CSS không áp dụng
**Kiểm tra:**
1. Console có errors không?
2. Tab có phải trang hợp lệ không? (http/https)
3. Thử refresh tab

### Lỗi: Popup không mở
**Giải pháp:**
1. Kiểm tra `popup.html` syntax
2. Xem console errors trong popup (right-click popup → Inspect)

## 📊 Debug Console

### Background Script Console
1. Vào `chrome://extensions/`
2. Click "Details" trên extension
3. Click "Inspect views: background page"
4. Xem Console tab

### Popup Console
1. Mở popup
2. Right-click trong popup
3. Click "Inspect"
4. Xem Console tab

### Content Script Console
1. Mở trang web bất kỳ
2. Press F12
3. Xem Console tab
4. Tìm messages từ "Green Theme"

## 🔍 Kiểm tra Extension hoạt động

### Test cơ bản:
```javascript
// Trong background console:
chrome.storage.sync.get(console.log);

// Trong popup console:
chrome.runtime.sendMessage({action: 'getState'}, console.log);
```

### Test CSS injection:
1. Bật theme
2. Mở tab mới với website
3. F12 → Elements → tìm `<meta id="green-theme-injected">`
4. F12 → Sources → Content scripts → green.css

## 📝 Logs hữu ích

Extension sẽ log các thông tin sau:
- ✅ "Green theme đã được áp dụng cho tab X"
- ✅ "Green theme đã được gỡ bỏ khỏi tab X"
- ℹ️ "Bỏ qua tab X: URL không hợp lệ"
- ℹ️ "Không thể truy cập tab X: Trang được bảo vệ"

## 🎯 Performance Tips

### Tối ưu hóa:
- Extension chỉ inject CSS khi cần
- Tự động detect và skip trang không hợp lệ
- Sử dụng marker để tránh duplicate injection
- Graceful error handling

### Memory usage:
- Background script chỉ chạy khi cần
- CSS được inject/remove sạch sẽ
- Không leak memory

## 🔄 Reset Extension

Nếu extension hoạt động không ổn định:

1. **Soft reset:**
   ```javascript
   // Trong background console:
   chrome.storage.sync.clear();
   chrome.runtime.reload();
   ```

2. **Hard reset:**
   - Remove extension
   - Restart browser
   - Load unpacked lại

## 📞 Báo cáo lỗi mới

Nếu gặp lỗi mới, cung cấp:
1. **Chrome version:** `chrome://version/`
2. **Console errors:** Screenshot hoặc copy text
3. **Steps to reproduce:** Chi tiết các bước
4. **Expected vs actual:** Mong đợi gì vs thực tế gì
5. **Website URL:** Nếu lỗi xảy ra trên trang cụ thể

---

**Extension hiện tại đã ổn định và sẵn sàng sử dụng! 🌿**
