# Hướng dẫn Test thủ công - Green Theme Extension

## Chuẩn bị Test

### 1. Cài đặt Extension
1. Mở Chrome/Edge
2. Vào `chrome://extensions/` hoặc `edge://extensions/`
3. <PERSON><PERSON>t "Developer mode"
4. <PERSON>lick "Load unpacked"
5. <PERSON><PERSON><PERSON> thư mục chứa extension
6. Kiểm tra extension xuất hiện trong danh sách

### 2. <PERSON><PERSON><PERSON> tra Icons
- Extension icon xuất hiện trên toolbar
- Click vào icon để mở popup
- Kiểm tra popup hiển thị đúng

## Test Cases

### TC01: Khởi tạo lần đầu
**M<PERSON>c tiêu:** Kiểm tra trạng thái mặc định
**B<PERSON>ớc thực hiện:**
1. Cài đặt extension lần đầu
2. Mở popup

**Kết quả mong đợi:**
- Toggle switch ở trạng thái OFF
- Status hiển thị "Green Theme đã tắt"
- <PERSON><PERSON><PERSON> "Làm mới tất cả tab" bị ẩn

### TC02: Bật Green Theme
**<PERSON><PERSON><PERSON> tiêu:** Kiểm tra chức năng bật theme
**<PERSON><PERSON><PERSON><PERSON> thực hiện:**
1. Click vào toggle switch
2. Quan sát thay đổi trên các tab đang mở

**Kết quả mong đợi:**
- Toggle switch chuyển sang ON
- Status hiển thị "Green Theme đang hoạt động"
- Tất cả tab hợp lệ áp dụng green theme
- Icon extension thay đổi (nếu có icon active)
- Nút "Làm mới tất cả tab" hiển thị

### TC03: Tắt Green Theme
**Mục tiêu:** Kiểm tra chức năng tắt theme
**Bước thực hiện:**
1. Từ trạng thái ON, click toggle switch
2. Quan sát thay đổi trên các tab

**Kết quả mong đợi:**
- Toggle switch chuyển sang OFF
- Tất cả tab trở về trạng thái ban đầu
- Không còn CSS tồn dư
- Icon extension trở về mặc định

### TC04: Tab mới khi theme ON
**Mục tiêu:** Kiểm tra auto-apply cho tab mới
**Bước thực hiện:**
1. Bật green theme
2. Mở tab mới với website bất kỳ
3. Navigate đến trang khác

**Kết quả mong đợi:**
- Tab mới tự động áp dụng green theme
- Theme áp dụng ngay khi trang load xong

### TC05: Tab mới khi theme OFF
**Mục tiêu:** Kiểm tra tab mới không bị ảnh hưởng
**Bước thực hiện:**
1. Tắt green theme
2. Mở tab mới với website bất kỳ

**Kết quả mong đợi:**
- Tab mới hiển thị bình thường
- Không có green theme

### TC06: Reload trang
**Mục tiêu:** Kiểm tra theme sau khi reload
**Bước thực hiện:**
1. Bật green theme
2. Reload một tab bất kỳ
3. Tắt theme và reload lại

**Kết quả mong đợi:**
- Theme được áp dụng lại sau reload (khi ON)
- Theme không xuất hiện sau reload (khi OFF)

### TC07: Khởi động lại trình duyệt
**Mục tiêu:** Kiểm tra persistence của state
**Bước thực hiện:**
1. Bật green theme
2. Đóng hoàn toàn trình duyệt
3. Mở lại trình duyệt
4. Kiểm tra popup

**Kết quả mong đợi:**
- State được lưu và khôi phục đúng
- Các tab cũ áp dụng theme khi reload

### TC08: Nút "Làm mới tất cả tab"
**Mục tiêu:** Kiểm tra chức năng refresh
**Bước thực hiện:**
1. Bật green theme
2. Click "Làm mới tất cả tab"
3. Quan sát loading state

**Kết quả mong đợi:**
- Nút hiển thị "Đang làm mới..."
- Tất cả tab được re-apply theme
- Hiển thị thông báo thành công

### TC09: Trang không hợp lệ
**Mục tiêu:** Kiểm tra xử lý trang chrome://
**Bước thực hiện:**
1. Mở tab `chrome://extensions/`
2. Mở tab `chrome://settings/`
3. Bật green theme

**Kết quả mong đợi:**
- Các trang chrome:// không bị ảnh hưởng
- Không có lỗi trong console
- Extension hoạt động bình thường với tab khác

### TC10: Multiple tabs
**Mục tiêu:** Kiểm tra với nhiều tab
**Bước thực hiện:**
1. Mở 5-10 tab với websites khác nhau
2. Bật/tắt green theme
3. Kiểm tra từng tab

**Kết quả mong đợi:**
- Tất cả tab hợp lệ được xử lý đồng thời
- Không có tab nào bị bỏ sót
- Performance ổn định

## Test với các loại website

### Websites nên test:
- **News sites:** vnexpress.net, dantri.com.vn
- **Social media:** facebook.com, twitter.com
- **E-commerce:** shopee.vn, tiki.vn
- **Search engines:** google.com, bing.com
- **Documentation:** developer.mozilla.org
- **GitHub:** github.com
- **YouTube:** youtube.com

### Kiểm tra:
- Layout không bị phá vỡ
- Text vẫn đọc được (contrast đủ)
- Buttons và links hoạt động bình thường
- Images không bị ảnh hưởng
- Responsive design vẫn hoạt động

## Debugging

### Console Logs
Mở Developer Tools (F12) và kiểm tra:
- Background script logs
- Content script logs
- Không có errors màu đỏ

### Storage
Kiểm tra chrome storage:
1. Vào `chrome://extensions/`
2. Click "Details" trên extension
3. Click "Inspect views: background page"
4. Console: `chrome.storage.sync.get(console.log)`

### Performance
- Extension không làm chậm browser
- CSS injection/removal nhanh chóng
- Memory usage ổn định

## Báo cáo lỗi

Khi phát hiện lỗi, ghi chú:
1. **Bước tái tạo:** Chi tiết các bước
2. **Kết quả thực tế:** Điều gì xảy ra
3. **Kết quả mong đợi:** Điều gì nên xảy ra
4. **Environment:** Chrome version, OS
5. **Console errors:** Screenshot hoặc copy text
6. **Website:** URL cụ thể nếu có

## Checklist hoàn thành

- [ ] TC01: Khởi tạo lần đầu
- [ ] TC02: Bật Green Theme  
- [ ] TC03: Tắt Green Theme
- [ ] TC04: Tab mới khi theme ON
- [ ] TC05: Tab mới khi theme OFF
- [ ] TC06: Reload trang
- [ ] TC07: Khởi động lại trình duyệt
- [ ] TC08: Nút "Làm mới tất cả tab"
- [ ] TC09: Trang không hợp lệ
- [ ] TC10: Multiple tabs
- [ ] Test với ít nhất 5 websites khác nhau
- [ ] Kiểm tra console không có errors
- [ ] Kiểm tra storage hoạt động đúng
- [ ] Performance ổn định
