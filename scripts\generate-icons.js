// Script tự động tạo icons cho Green Theme Extension
// Chạy: node scripts/generate-icons.js

const fs = require('fs');
const path = require('path');

// Tạo SVG icon
function createSVGIcon(size, isActive = false) {
  const bgColor = isActive ? '#4CAF50' : '#E0E0E0';
  const textColor = isActive ? '#FFFFFF' : '#757575';
  const fontSize = Math.floor(size * 0.6);
  
  return `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="${size}" height="${size}" rx="${Math.floor(size * 0.15)}" fill="${bgColor}"/>
  <text x="${size/2}" y="${size/2 + fontSize/3}" font-family="Arial, sans-serif" font-size="${fontSize}" font-weight="bold" text-anchor="middle" fill="${textColor}">G</text>
</svg>`;
}

// Tạo HTML để convert sang PNG
function createHTMLForIcon(size, isActive = false) {
  const bgColor = isActive ? '#4CAF50' : '#E0E0E0';
  const textColor = isActive ? '#FFFFFF' : '#757575';
  const fontSize = Math.floor(size * 0.6);
  
  return `<!DOCTYPE html>
<html>
<head>
  <style>
    body { margin: 0; padding: 0; }
    .icon {
      width: ${size}px;
      height: ${size}px;
      background: ${bgColor};
      border-radius: ${Math.floor(size * 0.15)}px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: Arial, sans-serif;
      font-size: ${fontSize}px;
      font-weight: bold;
      color: ${textColor};
    }
  </style>
</head>
<body>
  <div class="icon">G</div>
</body>
</html>`;
}

// Tạo thư mục nếu chưa có
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// Main function
function generateIcons() {
  const iconsDir = path.join(__dirname, '..', 'icons');
  const sizes = [16, 32, 48, 128];
  
  ensureDir(iconsDir);
  
  console.log('🎨 Tạo icons cho Green Theme Extension...\n');
  
  // Tạo SVG icons
  sizes.forEach(size => {
    // Icon mặc định
    const defaultSVG = createSVGIcon(size, false);
    const defaultPath = path.join(iconsDir, `icon${size}.svg`);
    fs.writeFileSync(defaultPath, defaultSVG);
    console.log(`✅ Tạo icon${size}.svg`);
    
    // Icon active
    const activeSVG = createSVGIcon(size, true);
    const activePath = path.join(iconsDir, `icon${size}-active.svg`);
    fs.writeFileSync(activePath, activeSVG);
    console.log(`✅ Tạo icon${size}-active.svg`);
  });
  
  // Tạo HTML templates để convert sang PNG
  const templatesDir = path.join(iconsDir, 'templates');
  ensureDir(templatesDir);
  
  sizes.forEach(size => {
    // Template mặc định
    const defaultHTML = createHTMLForIcon(size, false);
    const defaultPath = path.join(templatesDir, `icon${size}.html`);
    fs.writeFileSync(defaultPath, defaultHTML);
    
    // Template active
    const activeHTML = createHTMLForIcon(size, true);
    const activePath = path.join(templatesDir, `icon${size}-active.html`);
    fs.writeFileSync(activePath, activeHTML);
  });
  
  console.log(`\n📁 Tạo HTML templates trong ${templatesDir}`);
  
  // Tạo hướng dẫn convert
  const convertGuide = `# Hướng dẫn convert HTML sang PNG

## Cách 1: Sử dụng browser
1. Mở file HTML trong browser
2. Right-click → Inspect Element
3. Console: \`document.body.style.zoom = '1'\`
4. Screenshot hoặc sử dụng browser extension

## Cách 2: Sử dụng online tools
1. Vào htmlcsstoimage.com
2. Upload HTML content
3. Download PNG

## Cách 3: Sử dụng Puppeteer (nếu có Node.js)
\`\`\`bash
npm install puppeteer
node convert-to-png.js
\`\`\`

## Sau khi có PNG files:
1. Đặt vào thư mục icons/
2. Đặt tên: icon16.png, icon32.png, icon48.png, icon128.png
3. Và: icon16-active.png, icon32-active.png, icon48-active.png, icon128-active.png
4. Xóa thư mục templates/ nếu muốn
`;
  
  fs.writeFileSync(path.join(templatesDir, 'CONVERT-GUIDE.md'), convertGuide);
  
  console.log('\n🎯 Hoàn thành! Các bước tiếp theo:');
  console.log('1. Mở các file HTML trong icons/templates/');
  console.log('2. Screenshot hoặc convert sang PNG');
  console.log('3. Lưu PNG files vào icons/ với đúng tên');
  console.log('4. Xem CONVERT-GUIDE.md để biết thêm chi tiết');
  console.log('\n📝 Hoặc sử dụng SVG files trực tiếp (một số browser hỗ trợ)');
}

// Chạy script
if (require.main === module) {
  generateIcons();
}

module.exports = { generateIcons, createSVGIcon, createHTMLForIcon };
