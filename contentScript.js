// Content Script cho Green Theme Extension
// File này chủ yếu để hỗ trợ, logic chính được xử lý qua chrome.scripting API

// Kiểm tra xem green theme đã được áp dụng chưa
function isGreenThemeApplied() {
  return document.querySelector('#green-theme-injected') !== null;
}

// Lắng nghe messages từ background script (nếu cần)
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  switch (request.action) {
    case 'checkThemeStatus':
      sendResponse({ applied: isGreenThemeApplied() });
      break;
      
    case 'ping':
      sendResponse({ status: 'alive' });
      break;
  }
  return true;
});

// Observer để theo dõi thay đổi DOM (nếu trang web thay đổi động)
const observer = new MutationObserver((mutations) => {
  // Kiểm tra xem marker vẫn còn không
  const marker = document.querySelector('#green-theme-injected');
  if (!marker) {
    // Nếu marker bị mất, thông báo cho background script
    chrome.runtime.sendMessage({ 
      action: 'themeMarkerLost',
      url: window.location.href 
    });
  }
});

// Bắt đầu observe khi DOM ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    observer.observe(document.head, { 
      childList: true, 
      subtree: true 
    });
  });
} else {
  observer.observe(document.head, { 
    childList: true, 
    subtree: true 
  });
}

// Cleanup khi trang unload
window.addEventListener('beforeunload', () => {
  observer.disconnect();
});

// Log để debug (chỉ trong development)
console.log('Green Theme Content Script loaded on:', window.location.href);
