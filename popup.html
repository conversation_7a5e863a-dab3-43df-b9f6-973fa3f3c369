<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Green Theme Extension</title>
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      background: linear-gradient(135deg, #f0f8f0 0%, #e8f5e8 100%);
    }

    .header {
      text-align: center;
      margin-bottom: 20px;
    }

    .header h1 {
      margin: 0;
      font-size: 18px;
      color: #2d5a2d;
      font-weight: 600;
    }

    .header p {
      margin: 5px 0 0 0;
      font-size: 12px;
      color: #5a7a5a;
    }

    .toggle-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: white;
      padding: 15px;
      border-radius: 10px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      margin-bottom: 15px;
    }

    .toggle-label {
      font-size: 14px;
      font-weight: 500;
      color: #2d5a2d;
    }

    .toggle-switch {
      position: relative;
      width: 50px;
      height: 24px;
      background: #ccc;
      border-radius: 12px;
      cursor: pointer;
      transition: background 0.3s;
    }

    .toggle-switch.active {
      background: #4CAF50;
    }

    .toggle-slider {
      position: absolute;
      top: 2px;
      left: 2px;
      width: 20px;
      height: 20px;
      background: white;
      border-radius: 50%;
      transition: transform 0.3s;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .toggle-switch.active .toggle-slider {
      transform: translateX(26px);
    }

    .status {
      text-align: center;
      padding: 10px;
      border-radius: 8px;
      font-size: 13px;
      font-weight: 500;
      margin-bottom: 15px;
    }

    .status.enabled {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .status.disabled {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .actions {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .btn {
      padding: 10px 15px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 13px;
      font-weight: 500;
      transition: all 0.2s;
    }

    .btn-refresh {
      background: #e3f2fd;
      color: #1976d2;
      border: 1px solid #bbdefb;
    }

    .btn-refresh:hover {
      background: #bbdefb;
    }

    .btn-refresh:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .loading {
      display: none;
      text-align: center;
      color: #666;
      font-size: 12px;
      margin-top: 10px;
    }

    .footer {
      text-align: center;
      margin-top: 15px;
      font-size: 11px;
      color: #888;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🌿 Green Theme</h1>
    <p>Theme xanh dịu mắt cho web</p>
  </div>

  <div class="toggle-container">
    <span class="toggle-label">Bật Green Theme</span>
    <div class="toggle-switch" id="themeToggle">
      <div class="toggle-slider"></div>
    </div>
  </div>

  <div class="status" id="status">
    <span id="statusText">Đang tải...</span>
  </div>

  <div class="actions">
    <button class="btn btn-refresh" id="refreshBtn">
      🔄 Làm mới tất cả tab
    </button>
  </div>

  <div class="loading" id="loading">
    Đang xử lý...
  </div>

  <div class="footer">
    Green Theme Extension v1.0.0
  </div>

  <script src="popup.js"></script>
</body>
</html>
