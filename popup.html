<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Green Image Viewer</title>
  <style>
    body {
      width: 400px;
      height: 500px;
      margin: 0;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .header {
      text-align: center;
      margin-bottom: 20px;
    }

    .header h1 {
      margin: 0;
      font-size: 24px;
      color: #2d5a2d;
      font-weight: 600;
    }

    .header p {
      margin: 10px 0 0 0;
      font-size: 14px;
      color: #5a7a5a;
    }

    .image-container {
      background: white;
      padding: 20px;
      border-radius: 15px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
      margin-bottom: 20px;
      text-align: center;
    }

    .green-image {
      width: 300px;
      height: 200px;
      background: linear-gradient(45deg,
        #4CAF50 0%,
        #66BB6A 25%,
        #81C784 50%,
        #A5D6A7 75%,
        #C8E6C9 100%);
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 18px;
      font-weight: bold;
      text-shadow: 0 2px 4px rgba(0,0,0,0.3);
      box-shadow: 0 2px 10px rgba(76, 175, 80, 0.3);
      transition: transform 0.3s ease;
    }

    .green-image:hover {
      transform: scale(1.05);
    }

    .description {
      background: rgba(255, 255, 255, 0.9);
      padding: 15px;
      border-radius: 10px;
      text-align: center;
      color: #2d5a2d;
      font-size: 14px;
      line-height: 1.5;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .footer {
      text-align: center;
      margin-top: 15px;
      font-size: 12px;
      color: #888;
    }

    .emoji {
      font-size: 24px;
      margin: 10px 0;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🌿 Green Image Viewer</h1>
    <p>Hình ảnh màu xanh đẹp mắt</p>
  </div>

  <div class="image-container">
    <div class="green-image">
      🍃 Beautiful Green 🍃
    </div>
  </div>

  <div class="description">
    <div class="emoji">🌱</div>
    <strong>Màu xanh tự nhiên</strong><br>
    Mang lại cảm giác thư giãn và gần gũi với thiên nhiên.<br>
    Giúp giảm căng thẳng và tạo cảm giác bình yên.
    <div class="emoji">✨</div>
  </div>

  <div class="footer">
    Green Image Viewer v1.0.0
  </div>
</body>
</html>
